import User from '../../../../models/User.js';
import {
    ensureSubscription,
    FREE_TIER_PLAN_NAME_BACKEND,
    PRO_PLAN_NAME_BACKEND,
    getLimitSync,
} from './planConfig.js';

/**
 * @desc    Increment investor pitch generation count for applicable plans
 * @route   POST /api/users/me/increment-pitch-count
 * @access  Private
 */
export const incrementInvestorPitchCount = async (req, res) => {
    try {
        let user = await User.findById(req.user.id);
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        user = ensureSubscription(user);

        const { planName } = user.subscription;
        let limitReached = false;
        let message = "Pitch count successfully incremented.";
        let countIncremented = false;

        if (planName === FREE_TIER_PLAN_NAME_BACKEND) {
            const currentCount = user.subscription.freeTierInvestorPitchCount || 0;
            const limit = getLimitSync(planName, 'investorPitch', 'monthly');

            if (limit !== -1 && currentCount >= limit) {
                limitReached = true;
                message = `Starter plan investor pitch limit (${limit}/month) reached. Please upgrade to generate more.`;
            } else {
                user.subscription.freeTierInvestorPitchCount = currentCount + 1;
                countIncremented = true;
            }
        } else if (planName === PRO_PLAN_NAME_BACKEND) {
            const currentCount = user.subscription.proTierInvestorPitchCount || 0;
            const limit = getLimitSync(planName, 'investorPitch', 'monthly');

            if (limit !== -1 && currentCount >= limit) {
                limitReached = true;
                message = `Pro plan investor pitch limit (${limit}/month) reached.`;
            } else {
                user.subscription.proTierInvestorPitchCount = currentCount + 1;
                countIncremented = true;
            }
        } else {
            return res.status(400).json({ message: "User is not on a plan that supports investor pitch generation." });
        }

        if (limitReached) {
            return res.status(403).json({ message });
        }

        if (countIncremented) {
            await user.save();
        }

        res.json({
            message,
            subscription: user.subscription,
        });

    } catch (error) {
        console.error('Increment Investor Pitch Count Error:', error);
        res.status(500).json({ message: 'Server error while incrementing investor pitch count.' });
    }
};
