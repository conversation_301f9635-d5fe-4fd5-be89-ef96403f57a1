import User from '../../../../models/User.js';
import {
    ensureSubscription,
    FREE_TIER_PLAN_NAME_BACKEND,
    PRO_PLAN_NAME_BACKEND,
    getLimitSync,
} from './planConfig.js';

/**
 * @desc    Increment PDF upload count for applicable plans
 * @route   POST /api/users/me/increment-upload-count
 * @access  Private
 */
export const incrementPdfUploadCount = async (req, res) => {
  try {
    let user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    user = ensureSubscription(user);

    let message = 'User is not on a plan that tracks uploads, count not incremented.';
    let countIncremented = false;
    let limitReached = false;

    if (user.subscription.planName === FREE_TIER_PLAN_NAME_BACKEND) {
        if (user.subscription.freeTierUploadCount >= STARTER_UPLOAD_LIMIT) {
            limitReached = true;
            message = "Starter plan upload limit reached.";
        } else {
            user.subscription.freeTierUploadCount = (user.subscription.freeTierUploadCount || 0) + 1;
            message = 'Starter plan upload count incremented successfully.';
            countIncremented = true;
        }
    } else if (user.subscription.planName === PRO_PLAN_NAME_BACKEND) {
        if (user.subscription.proTierUploadCount >= PRO_UPLOAD_LIMIT) {
            limitReached = true;
            message = "Pro plan upload limit reached.";
        } else {
            user.subscription.proTierUploadCount = (user.subscription.proTierUploadCount || 0) + 1;
            message = 'Pro plan upload count incremented successfully.';
            countIncremented = true;
        }
    }

    if (limitReached) {
        return res.status(403).json({ message });
    }

    if (countIncremented) {
      await user.save();
    }

    res.json({
      message: message,
      subscription: user.subscription,
    });

  } catch (error) {
    console.error('Increment PDF Upload Count Error:', error);
    res.status(500).json({ message: 'Server error while incrementing upload count.' });
  }
};