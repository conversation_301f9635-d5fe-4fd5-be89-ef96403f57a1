// common/user/limit/planConfig.js
import SubscriptionLimits from '../../../../models/SubscriptionLimits.js';
import cacheService from '../../../../services/cacheService.js';

// Plan Names
export const FREE_TIER_PLAN_NAME_BACKEND = 'Starter';
export const PRO_PLAN_NAME_BACKEND = 'Pro';

// --- FALLBACK LIMITS (Used when database is unavailable) ---
const FALLBACK_LIMITS = {
    'Starter': {
        businessPlans: { monthly: 3 },
        pdfUploads: { monthly: 5 },
        investorPitches: { monthly: 3 },
        businessQA: { daily: 5 },
        chatMessages: { daily: 20 }
    },
    'Pro': {
        businessPlans: { monthly: 15 },
        pdfUploads: { monthly: 25 },
        investorPitches: { monthly: 15 },
        businessQA: { daily: 25 },
        chatMessages: { daily: 100 }
    }
};

// --- LEGACY CONSTANTS (Deprecated - kept for backward compatibility) ---
export const STARTER_BUSINESS_PLAN_LIMIT = 3;
export const STARTER_UPLOAD_LIMIT = 5;
export const STARTER_INVESTOR_PITCH_LIMIT = 3;
export const STARTER_BUSINESS_QA_LIMIT = 5;
export const STARTER_MESSAGE_LIMIT = 20;

export const PRO_UPLOAD_LIMIT = 25;
export const PRO_BUSINESS_PLAN_LIMIT = 15;
export const PRO_INVESTOR_PITCH_LIMIT = 15;
export const PRO_BUSINESS_QA_LIMIT = 25;
export const PRO_MESSAGE_LIMIT = 100;


// Helper function to check if a limit should be enforced for a plan
export const shouldEnforceLimit = (planName) => {
    // Limits are now enforced on BOTH plans.
    return planName === FREE_TIER_PLAN_NAME_BACKEND || planName === PRO_PLAN_NAME_BACKEND;
};

/**
 * Get dynamic limit for a plan and feature from database
 * Falls back to cached data or hardcoded values if database is unavailable
 */
export const getLimit = async (planName, feature, period = 'monthly') => {
    try {
        // Try to get from cache first
        let plan = cacheService.getSubscriptionPlan(planName);

        if (!plan) {
            // If not in cache, fetch from database
            plan = await SubscriptionLimits.findOne({ planName, isActive: true });
            if (plan) {
                cacheService.setSubscriptionPlan(planName, plan);
            }
        }

        if (plan && plan.limits) {
            // Map feature names to database schema
            const featureMap = {
                'businessPlan': 'businessPlans',
                'upload': 'pdfUploads',
                'investorPitch': 'investorPitches',
                'businessQA': 'businessQA',
                'message': 'chatMessages'
            };

            const dbFeatureName = featureMap[feature] || feature;
            const limit = plan.limits[dbFeatureName]?.[period];

            if (limit !== undefined) {
                return limit;
            }
        }

        // Fallback to hardcoded values if database lookup fails
        console.warn(`Using fallback limit for ${planName}.${feature}.${period}`);
        return getFallbackLimit(planName, feature, period);

    } catch (error) {
        console.error('Error fetching dynamic limit:', error);
        return getFallbackLimit(planName, feature, period);
    }
};

/**
 * Synchronous version of getLimit for backward compatibility
 * Uses cached data or fallback values
 */
export const getLimitSync = (planName, feature, period = 'monthly') => {
    try {
        // Try to get from cache
        const plan = cacheService.getSubscriptionPlan(planName);

        if (plan && plan.limits) {
            const featureMap = {
                'businessPlan': 'businessPlans',
                'upload': 'pdfUploads',
                'investorPitch': 'investorPitches',
                'businessQA': 'businessQA',
                'message': 'chatMessages'
            };

            const dbFeatureName = featureMap[feature] || feature;
            const limit = plan.limits[dbFeatureName]?.[period];

            if (limit !== undefined) {
                return limit;
            }
        }

        // Fallback to hardcoded values
        return getFallbackLimit(planName, feature, period);

    } catch (error) {
        console.error('Error getting cached limit:', error);
        return getFallbackLimit(planName, feature, period);
    }
};

/**
 * Get fallback limit values
 */
const getFallbackLimit = (planName, feature, period = 'monthly') => {
    const fallbackLimits = FALLBACK_LIMITS[planName];
    if (!fallbackLimits) return 0;

    const featureMap = {
        'businessPlan': 'businessPlans',
        'upload': 'pdfUploads',
        'investorPitch': 'investorPitches',
        'businessQA': 'businessQA',
        'message': 'chatMessages'
    };

    const dbFeatureName = featureMap[feature] || feature;
    return fallbackLimits[dbFeatureName]?.[period] || 0;
};

// Helper function to initialize subscription if it's missing
export const ensureSubscription = (user) => {
    if (!user.subscription) {
        user.subscription = {
            planName: FREE_TIER_PLAN_NAME_BACKEND,
            status: 'active',
            startDate: new Date(),
            freeTierUploadCount: 0,
            proTierUploadCount: 0,
            freeTierMessageCount: 0,
            proTierMessageCount: 0,
            freeTierBusinessPlanCount: 0,
            proTierBusinessPlanCount: 0,
            freeTierInvestorPitchCount: 0,
            proTierInvestorPitchCount: 0,
            freeTierBusinessQACount: 0,
            proTierBusinessQACount: 0,
            businessPlanMonthlyReset: new Date(),
            investorPitchMonthlyReset: new Date(),
            businessQADailyReset: new Date(),
        };
    }
    return user;
};