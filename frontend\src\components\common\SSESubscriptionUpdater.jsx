// components/common/SSESubscriptionUpdater.jsx
import { useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import useSSE from '../../hooks/useSSE';

/**
 * Component that listens for subscription plan updates via SSE
 * and refreshes user data when the user's plan limits are changed
 * This ensures real-time synchronization of limits in the sidebar
 */
const SSESubscriptionUpdater = () => {
    const { currentUser, refreshUser } = useAuth();
    const API_BASE_URL = import.meta.env.VITE_NODE_BACKEND_URL || 'http://localhost:3001';

    // Handle SSE events for subscription plan updates
    const handleSSEEvent = async (eventType, data) => {
        console.log('SSE Subscription Updater - Event received:', eventType, data);

        // Only process events if user is authenticated
        if (!currentUser) return;

        const userPlanName = currentUser.subscription?.planName;
        
        switch (eventType) {
            case 'subscription-plan-updated':
            case 'subscription-plan-reset':
                // Check if the updated plan matches the user's current plan
                if (data.planName === userPlanName) {
                    console.log(`User's plan "${userPlanName}" was updated. Refreshing user data...`);
                    
                    // Refresh user data to get updated subscription info
                    const result = await refreshUser();
                    if (result.success) {
                        console.log('User data refreshed successfully after plan update');
                    } else {
                        console.error('Failed to refresh user data after plan update:', result.error);
                    }
                }
                break;
            case 'pricing-updated':
                // Pricing updates might also affect the user's plan
                if (data.planName === userPlanName) {
                    console.log(`User's plan "${userPlanName}" pricing was updated. Refreshing user data...`);
                    
                    const result = await refreshUser();
                    if (result.success) {
                        console.log('User data refreshed successfully after pricing update');
                    } else {
                        console.error('Failed to refresh user data after pricing update:', result.error);
                    }
                }
                break;
            default:
                // Handle other events if needed
                break;
        }
    };

    // Setup SSE connection for real-time updates
    const { isConnected: sseConnected, error: sseError } = useSSE(
        `${API_BASE_URL}/api/sse/connect`,
        {
            onEvent: handleSSEEvent,
            onError: (error) => {
                console.warn('SSE Subscription Updater Error (non-critical):', error);
            },
            enabled: !!currentUser // Only connect if user is authenticated
        }
    );

    // Log connection status for debugging
    useEffect(() => {
        if (currentUser) {
            console.log('SSE Subscription Updater - Connection status:', {
                connected: sseConnected,
                userPlan: currentUser.subscription?.planName,
                error: sseError
            });
        }
    }, [sseConnected, sseError, currentUser]);

    // This component doesn't render anything - it's just for side effects
    return null;
};

export default SSESubscriptionUpdater;
