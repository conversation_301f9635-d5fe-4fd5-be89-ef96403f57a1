import React, { useState } from 'react';
import { FiUser, <PERSON>LogOut, FiLogIn, FiChevronUp, FiStar, FiUpload, FiFileText, FiTarget, FiHelpCircle } from 'react-icons/fi';
import IconWrapper from './IconWrapper';
import { Link, useLocation } from 'react-router-dom';
import { useUserSubscriptionLimits } from '../../useSubscriptionPlans';

// --- Plan Constants ---
const FREE_TIER_PLAN_NAME = 'Starter';
const PRO_PLAN_NAME = 'Pro';

// Reusable stat component for displaying usage bars
const UsageStat = ({ icon, title, count, limit, isUnlimited = false, period = "" }) => {
  const percentage = limit > 0 && !isUnlimited ? (count / limit) * 100 : 0;
  const isExceeded = !isUnlimited && count >= limit;

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {icon}
          <span className="text-xs text-gray-300">{title}</span>
        </div>
        <span className="text-xs font-medium text-gray-200">
          {isUnlimited ? `${count} / ∞` : `${count} / ${limit}`}
          {period && <span className="text-gray-400 text-xs ml-1">{period}</span>}
        </span>
      </div>
      <div className="w-full bg-[#2C2F33] rounded-full h-2 overflow-hidden">
        <div
          className={`h-full transition-all duration-500 ease-out rounded-full
            ${isUnlimited
              ? 'bg-gradient-to-r from-green-500 to-emerald-500'
              : isExceeded
                ? 'bg-gradient-to-r from-red-600 to-orange-600'
                : percentage >= 80
                  ? 'bg-gradient-to-r from-yellow-500 to-orange-500'
                  : 'bg-gradient-to-r from-sky-600 to-cyan-600'
            }`}
          style={{ width: isUnlimited ? '100%' : `${Math.min(percentage, 100)}%` }}
        />
      </div>
    </div>
  );
};


const SidebarFooter = ({ isAuthenticated, currentUser, onLogoutClick, onAuthActionClick, isCollapsed }) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const location = useLocation();

  const isPdfSection = location.pathname.includes('/pdf');
  const isBusinessSection = location.pathname.includes('/business');

  if (!isAuthenticated || !currentUser) {
    const commonButtonClass = "w-full flex items-center justify-center px-4 py-2.5 rounded-xl text-white text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-[#1A1C1E] group";
    const collapsedButtonClass = "w-10 h-10 rounded-lg bg-sky-700 hover:bg-sky-600 text-white transition-all duration-200 flex items-center justify-center hover:scale-105 shadow-lg hover:shadow-xl";
    return (
      <div className={`p-4 mt-auto border-t border-[#2C2F33]/30 shrink-0 ${isCollapsed ? 'p-3' : ''}`}>
        <button onClick={onAuthActionClick} className={isCollapsed ? collapsedButtonClass : `${commonButtonClass} bg-gradient-to-r from-sky-700 to-cyan-700 hover:from-sky-600 hover:to-cyan-600 hover:scale-[1.02] hover:shadow-lg hover:shadow-sky-950/30 focus:ring-sky-500`} title="Login / Register">
          {isCollapsed ? <FiLogIn className="w-4 h-4" /> : <><IconWrapper><FiLogIn className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-200" /></IconWrapper>Login / Register</>}
        </button>
      </div>
    );
  }

  const { subscription, name, email } = currentUser;
  const userPlan = subscription?.planName || FREE_TIER_PLAN_NAME;
  const isPro = userPlan === PRO_PLAN_NAME;

  // Use dynamic limits hook to get real-time limits for the user's plan
  const {
    limits: userLimits,
    loading: limitsLoading,
    getLimit,
    isUnlimited
  } = useUserSubscriptionLimits(userPlan);

  const renderRouteSpecificStats = () => {
    if (!subscription || limitsLoading || !userLimits) return null;
    const stats = [];
    let showUpgrade = false;

    if (isPdfSection) {
      const count = (isPro ? subscription.proTierUploadCount : subscription.freeTierUploadCount) || 0;
      const uploadLimit = getLimit('pdfUploads', 'monthly');
      const isUploadUnlimited = isUnlimited('pdfUploads', 'monthly');

      stats.push(
        <UsageStat
          key="uploads"
          icon={<FiUpload className="w-3 h-3 text-gray-400" />}
          title="PDF Uploads"
          count={count}
          limit={uploadLimit}
          isUnlimited={isUploadUnlimited}
        />
      );
      if (!isPro && !isUploadUnlimited && count >= uploadLimit) showUpgrade = true;
    }

    if (isBusinessSection) {
      const planCount = (isPro ? subscription.proTierBusinessPlanCount : subscription.freeTierBusinessPlanCount) || 0;
      const pitchCount = (isPro ? subscription.proTierInvestorPitchCount : subscription.freeTierInvestorPitchCount) || 0;
      const qaCount = (isPro ? subscription.proTierBusinessQACount : subscription.freeTierBusinessQACount) || 0;

      const planLimit = getLimit('businessPlans', 'monthly');
      const pitchLimit = getLimit('investorPitches', 'monthly');
      const qaLimit = getLimit('businessQA', 'daily');

      const isPlanUnlimited = isUnlimited('businessPlans', 'monthly');
      const isPitchUnlimited = isUnlimited('investorPitches', 'monthly');
      const isQAUnlimited = isUnlimited('businessQA', 'daily');

      stats.push(
        <UsageStat
          key="plans"
          icon={<FiFileText className="w-3 h-3 text-gray-400" />}
          title="Business Plans"
          count={planCount}
          limit={planLimit}
          isUnlimited={isPlanUnlimited}
          period={isPro ? "p/mo" : ""}
        />
      );
      stats.push(
        <UsageStat
          key="pitches"
          icon={<FiTarget className="w-3 h-3 text-gray-400" />}
          title="Investor Pitches"
          count={pitchCount}
          limit={pitchLimit}
          isUnlimited={isPitchUnlimited}
          period={isPro ? "p/mo" : ""}
        />
      );
      stats.push(
        <UsageStat
          key="qa"
          icon={<FiHelpCircle className="w-3 h-3 text-gray-400" />}
          title="Business Q&A"
          count={qaCount}
          limit={qaLimit}
          isUnlimited={isQAUnlimited}
          period={isPro ? "p/day" : ""}
        />
      );

      if (!isPro && (
        (!isPlanUnlimited && planCount >= planLimit) ||
        (!isPitchUnlimited && pitchCount >= pitchLimit) ||
        (!isQAUnlimited && qaCount >= qaLimit)
      )) {
        showUpgrade = true;
      }
    }

    if (stats.length === 0) return null;

    return (
      <div className="space-y-4">
        {stats}
        {showUpgrade && <Link to="/pricing" className="inline-flex items-center space-x-1 text-xs font-medium text-yellow-400 hover:text-yellow-300 transition-colors duration-200 hover:underline pt-2"><FiStar className="w-3 h-3" /><span>Upgrade for Higher Limits</span></Link>}
      </div>
    );
  };
  
  if (isCollapsed) {
    return (
      <div className="p-3 mt-auto border-t border-[#2C2F33]/30 shrink-0">
        <div className="flex flex-col items-center space-y-3">
          <div className="relative group/avatar">
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-sky-600 to-cyan-700 flex items-center justify-center text-white font-semibold text-sm shadow-lg hover:shadow-xl transition-all duration-200 cursor-pointer hover:scale-105">{name ? name.charAt(0).toUpperCase() : email.charAt(0).toUpperCase()}</div>
            {isPro && <FiStar className="absolute -bottom-1 -right-1 w-4 h-4 text-yellow-400 bg-[#151719] rounded-full p-0.5" />}
            <div className="absolute left-full ml-3 top-1/2 transform -translate-y-1/2 bg-[#1A1C1E] text-white px-3 py-2 rounded-lg text-sm opacity-0 invisible group-hover/avatar:opacity-100 group-hover/avatar:visible transition-all duration-200 delay-300 z-50 border border-[#2C2F33] shadow-xl min-w-max">
              <div className="font-medium">{name || email}</div><div className="text-xs text-gray-400 mt-1">Plan: {userPlan}</div><div className="absolute right-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-r-[#1A1C1E]" />
            </div>
          </div>
          <button onClick={onLogoutClick} className="w-10 h-10 rounded-lg bg-red-700/80 hover:bg-red-600/90 text-white transition-all duration-200 flex items-center justify-center hover:scale-105 shadow-lg hover:shadow-xl group/logout" title="Logout"><FiLogOut className="w-4 h-4 group-hover/logout:scale-110 transition-transform duration-200" /></button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 mt-auto border-t border-[#2C2F33]/30 shrink-0">
      <div className="space-y-4">
        <div className="relative cursor-pointer" onClick={() => setIsExpanded(!isExpanded)}>
          <div className="flex items-center p-3 rounded-xl bg-[#1A1C1E]/70 hover:bg-[#232527]/70 transition-all duration-200 group/user border border-[#2C2F33]/50">
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-sky-600 to-cyan-700 flex items-center justify-center text-white font-semibold text-sm shadow-lg group-hover/user:shadow-xl transition-all duration-200 group-hover/user:scale-105 mr-3">{name ? name.charAt(0).toUpperCase() : email.charAt(0).toUpperCase()}</div>
            <div className="flex-1 min-w-0"><div className="flex items-center space-x-2"><span className="text-sm font-medium text-gray-100 truncate">{name || email}</span>{isPro && <FiStar className="w-4 h-4 text-yellow-400 flex-shrink-0" title="Pro Plan" />}</div>{name && email && (<span className="text-xs text-gray-400 truncate block">{email}</span>)}</div><FiChevronUp className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`} />
          </div>
        </div>
        {isExpanded && (
          <div className="space-y-4 px-1 animate-in slide-in-from-top-2 duration-300">
            <div className="flex items-center justify-between p-3 rounded-lg bg-[#151719]/50 border border-[#232527]/40"><div className="flex items-center space-x-2"><div className={`w-2 h-2 rounded-full ${isPro ? 'bg-yellow-500' : 'bg-sky-500'}`} /><span className="text-xs text-gray-300">Current Plan</span></div><span className={`text-xs font-semibold ${isPro ? 'text-yellow-400' : 'text-sky-400'}`}>{userPlan}</span></div>
            {renderRouteSpecificStats()}
          </div>
        )}
        <button onClick={onLogoutClick} className="w-full flex items-center justify-center px-4 py-2.5 rounded-xl bg-gradient-to-r from-red-700 to-red-600 hover:from-red-600 hover:to-red-500 text-white text-sm font-medium transition-all duration-200 hover:scale-[1.02] hover:shadow-lg hover:shadow-red-950/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-[#1A1C1E] group/logout"><IconWrapper><FiLogOut className="w-4 h-4 mr-2 group-hover/logout:scale-110 transition-transform duration-200" /></IconWrapper>Logout</button>
      </div>
    </div>
  );
};

export default SidebarFooter;