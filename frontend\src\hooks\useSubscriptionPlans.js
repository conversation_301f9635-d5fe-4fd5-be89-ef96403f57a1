// hooks/useSubscriptionPlans.js
import { useState, useEffect, useCallback } from 'react';
import useSSE from './useSSE';

/**
 * Custom hook for managing subscription plans with real-time updates
 */
export const useSubscriptionPlans = () => {
    const [plans, setPlans] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [lastUpdated, setLastUpdated] = useState(null);

    const API_BASE_URL = import.meta.env.VITE_NODE_BACKEND_URL || 'http://localhost:3001';

    // Fetch plans from API
    const fetchPlans = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);

            const response = await fetch(`${API_BASE_URL}/api/subscription/plans`);
            
            if (!response.ok) {
                throw new Error(`Failed to fetch plans: ${response.status}`);
            }

            const data = await response.json();
            
            if (data.success && data.plans) {
                // Transform backend data to frontend format
                const transformedPlans = data.plans.map(plan => ({
                    id: plan.id,
                    tierName: plan.displayName,
                    planName: plan.planName,
                    price: formatPrice(plan.price.monthly),
                    billingCycle: 'Per month',
                    features: plan.features,
                    actionType: plan.actionType,
                    buttonText: plan.buttonText,
                    description: plan.description,
                    paymentProcessor: plan.paymentProcessor,
                    isPopular: plan.planName === 'Pro', // Mark Pro as popular
                    // Add yearly pricing info
                    yearlyPrice: formatPrice(plan.price.yearly),
                    monthlyAmount: plan.price.monthly,
                    yearlyAmount: plan.price.yearly
                }));

                setPlans(transformedPlans);
                setLastUpdated(data.lastUpdated);
                console.log('Subscription plans loaded:', transformedPlans.length);
            } else {
                throw new Error('Invalid response format');
            }
        } catch (err) {
            console.error('Error fetching subscription plans:', err);
            setError(err.message);
            
            // Fallback to empty array on error
            setPlans([]);
        } finally {
            setLoading(false);
        }
    }, [API_BASE_URL]);

    // Handle SSE events for real-time updates
    const handleSSEEvent = useCallback((eventType, data) => {
        console.log('SSE Event received:', eventType, data);

        switch (eventType) {
            case 'subscription-plan-updated':
            case 'subscription-plan-reset':
                // Refresh plans when any plan is updated
                console.log('Plan updated via SSE, refreshing...');
                fetchPlans();
                break;
            case 'pricing-updated':
                // Handle specific pricing updates
                console.log('Pricing updated via SSE, refreshing...');
                fetchPlans();
                break;
            default:
                // Handle other events if needed
                break;
        }
    }, [fetchPlans]);

    // Setup SSE connection for real-time updates
    const { isConnected: sseConnected, error: sseError } = useSSE(
        `${API_BASE_URL}/api/sse/connect`,
        {
            onEvent: handleSSEEvent,
            onError: (error) => {
                console.warn('SSE Error (non-critical):', error);
                // SSE errors are non-critical, don't set main error state
            },
            enabled: true // Always try to connect for real-time updates
        }
    );

    // Initial load
    useEffect(() => {
        fetchPlans();
    }, [fetchPlans]);

    // Format price for display
    const formatPrice = (price) => {
        if (price === 0) return 'Free';
        if (price === null || price === undefined) return 'Custom';
        return `$${price.toFixed(2)}`;
    };

    // Get plan by name
    const getPlanByName = useCallback((planName) => {
        return plans.find(plan => plan.planName === planName);
    }, [plans]);

    // Refresh plans manually
    const refresh = useCallback(() => {
        return fetchPlans();
    }, [fetchPlans]);

    return {
        plans,
        loading,
        error,
        lastUpdated,
        sseConnected,
        sseError,
        refresh,
        getPlanByName,
        // Utility functions
        isLoaded: !loading && !error && plans.length > 0,
        isEmpty: !loading && !error && plans.length === 0
    };
};

export default useSubscriptionPlans;

/**
 * Custom hook for managing user's current subscription limits with real-time updates
 * This hook specifically handles the user's current plan limits and updates them
 * when admin changes limits through the dashboard
 */
export const useUserSubscriptionLimits = (userPlanName) => {
    const [limits, setLimits] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [lastUpdated, setLastUpdated] = useState(null);

    const API_BASE_URL = import.meta.env.VITE_NODE_BACKEND_URL || 'http://localhost:3001';

    // Fetch limits for the user's current plan
    const fetchUserLimits = useCallback(async () => {
        if (!userPlanName) {
            setLimits(null);
            setLoading(false);
            return;
        }

        try {
            setLoading(true);
            setError(null);

            const response = await fetch(`${API_BASE_URL}/api/subscription/plans/${userPlanName}`);

            if (!response.ok) {
                throw new Error(`Failed to fetch limits for plan: ${userPlanName}`);
            }

            const data = await response.json();
            setLimits(data.plan?.limits || null);
            setLastUpdated(new Date().toISOString());
        } catch (err) {
            console.error('Error fetching user subscription limits:', err);
            setError(err.message);
            setLimits(null);
        } finally {
            setLoading(false);
        }
    }, [userPlanName, API_BASE_URL]);

    // Handle SSE events for real-time limit updates
    const handleSSEEvent = useCallback((eventType, data) => {
        console.log('User Limits SSE Event:', eventType, data);

        switch (eventType) {
            case 'subscription-plan-updated':
            case 'subscription-plan-reset':
                // Check if the updated plan matches the user's current plan
                if (data.planName === userPlanName) {
                    console.log(`User's plan "${userPlanName}" limits updated via SSE, refreshing...`);
                    fetchUserLimits();
                }
                break;
            case 'pricing-updated':
                // Pricing updates might also include limit changes
                if (data.planName === userPlanName) {
                    console.log(`User's plan "${userPlanName}" pricing updated via SSE, refreshing...`);
                    fetchUserLimits();
                }
                break;
            default:
                break;
        }
    }, [userPlanName, fetchUserLimits]);

    // Setup SSE connection for real-time updates
    const { isConnected: sseConnected, error: sseError } = useSSE(
        `${API_BASE_URL}/api/sse/connect`,
        {
            onEvent: handleSSEEvent,
            onError: (error) => {
                console.warn('User Limits SSE Error (non-critical):', error);
            },
            enabled: !!userPlanName // Only connect if user has a plan
        }
    );

    // Initial load and reload when plan changes
    useEffect(() => {
        fetchUserLimits();
    }, [fetchUserLimits]);

    // Helper function to get a specific limit value
    const getLimit = useCallback((category, period = 'monthly') => {
        if (!limits || !limits[category]) return 0;
        return limits[category][period] || 0;
    }, [limits]);

    // Helper function to check if a limit is unlimited (-1)
    const isUnlimited = useCallback((category, period = 'monthly') => {
        const limit = getLimit(category, period);
        return limit === -1;
    }, [getLimit]);

    // Refresh limits manually
    const refresh = useCallback(() => {
        return fetchUserLimits();
    }, [fetchUserLimits]);

    return {
        limits,
        loading,
        error,
        lastUpdated,
        sseConnected,
        sseError,
        refresh,
        getLimit,
        isUnlimited,
        // Utility functions
        isLoaded: !loading && !error && limits !== null,
        isEmpty: !loading && !error && limits === null
    };
};
